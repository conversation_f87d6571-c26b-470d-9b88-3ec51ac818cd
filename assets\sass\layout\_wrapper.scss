///
/// Multiverse by HTML5 UP
/// html5up.net | @ajlkn
/// Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
///

/* Wrapper */

	#wrapper {
		@include vendor('transition', (
			'-moz-filter #{_duration(panel)} ease',
			'-webkit-filter #{_duration(panel)} ease',
			'-ms-filter #{_duration(panel)} ease',
			'filter #{_duration(panel)} ease'
		));
		position: relative;

		&:after {
			@include vendor('pointer-events', 'none');
			@include vendor('transition', (
				'opacity #{_duration(modal)} ease',
				'visibility #{_duration(modal)}',
			));
			background: _palette(bg-overlay-alt);
			content: '';
			display: block;
			height: 100%;
			left: 0;
			opacity: 0;
			position: absolute;
			top: 0;
			visibility: hidden;
			width: 100%;
			z-index: 1;

			body.ie & {
				background: _palette(bg-ie-overlay-alt);
			}
		}

		body.modal-active & {
			@include vendor('filter', 'blur(8px)');

			&:after {
				@include vendor('pointer-events', 'auto');
				opacity: 1;
				visibility: visible;
				z-index: _misc(z-index-base) + 3;
			}
		}

		&:before {
			@include vendor('animation', 'spinner 1s infinite linear !important');
			@include vendor('pointer-events', 'none');
			@include vendor('transition', (
				'top 0.75s ease-in-out',
				'opacity 0.35s ease-out',
				'visibility 0.35s'
			));
			background-image: url('images/spinner.svg');
			background-position: center;
			background-repeat: no-repeat;
			background-size: contain;
			content: '';
			display: block;
			font-size: 2em;
			height: 2em;
			left: 50%;
			line-height: 2em;
			margin: -1em 0 0 -1em;
			opacity: 0;
			position: fixed;
			text-align: center;
			top: 75%;
			visibility: hidden;
			width: 2em;
		}

		body.is-preload & {
			&:before {
				@include vendor('transition', 'opacity 1s ease-out !important');
				@include vendor('transition-delay', '0.5s !important');
				opacity: 0.25;
				top: 50%;
				visibility: visible;
			}
		}
	}