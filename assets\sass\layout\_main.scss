///
/// Multiverse by HTML5 UP
/// html5up.net | @ajlkn
/// Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
///

/* Main */

	#main {
		@include vendor('transition', (
			'-moz-filter #{_duration(panel)} ease',
			'-webkit-filter #{_duration(panel)} ease',
			'-ms-filter #{_duration(panel)} ease',
			'filter #{_duration(panel)} ease'
		));
		@include vendor('display', 'flex');
		@include vendor('flex-wrap', 'wrap');
		-webkit-tap-highlight-color: rgba(255,255,255,0);

		.thumb {
			@include vendor('transition', (
				'opacity 1.25s ease-in-out'
			));
			@include vendor('pointer-events', 'auto');
			-webkit-tap-highlight-color: rgba(255,255,255,0);
			opacity: 1;
			overflow: hidden;
			position: relative;

			&:after {
				@include vendor('background-image', 'linear-gradient(to top, rgba(10,17,25,0.35) 5%, rgba(10,17,25,0) 35%)');
				@include vendor('pointer-events', 'none');
				background-size: cover;
				content: '';
				display: block;
				height: 100%;
				left: 0;
				position: absolute;
				top: 0;
				width: 100%;
			}

			> .image {
				-webkit-tap-highlight-color: rgba(255,255,255,0);
				background-position: center;
				background-repeat: no-repeat;
				background-size: cover;
				border: 0;
				height: 100%;
				left: 0;
				position: absolute;
				top: 0;
				width: 100%;
			}

			> h2 {
				@include vendor('pointer-events', 'none');
				bottom: (1.5em / 0.8);
				font-size: 0.8em;
				left: (1.75em / 0.8);
				margin: 0;
				position: absolute;
				z-index: 1;
			}

			> p {
				display: none;
			}
		}

		&:after {
			@include vendor('pointer-events', 'none');
			@include vendor('transition', (
				'opacity #{_duration(panel)} ease',
				'visibility #{_duration(panel)}',
			));
			background: _palette(bg-overlay);
			content: '';
			display: block;
			height: 100%;
			left: 0;
			opacity: 0;
			position: absolute;
			top: 0;
			visibility: hidden;
			width: 100%;
			z-index: 1;

			body.ie & {
				background: _palette(bg-ie-overlay);
			}
		}

		body.content-active & {
			@include vendor('filter', 'blur(6px)');

			&:after {
				@include vendor('pointer-events', 'auto');
				opacity: 1;
				visibility: visible;
			}
		}

		body.is-preload & {
			.thumb {
				@include vendor('pointer-events', 'none');
				opacity: 0;
			}
		}

		@mixin thumb($rows, $columns, $pad, $minHeight) {
			$baseDelay: _duration(header) - 0.5;
			$defaultDelay: $baseDelay + (((($rows * $columns) + 1) * 1.5) * _duration(thumb));

			.thumb {
				@include vendor('transition-delay', '#{$defaultDelay}');
				height: calc(#{100vh / ($rows + $pad)} - #{_size(header) / $rows});
				min-height: $minHeight;
				width: (100% / $columns);

				@for $i from 1 through (($rows * $columns) * 1.5) {
					&:nth-child(#{$i}) {
						@include vendor('transition-delay', '#{$baseDelay + ($i * _duration(thumb))}');
					}
				}
			}
		}

		// Default.
			@include thumb(
				_misc(main-layout, default, rows),
				_misc(main-layout, default, columns),
				_misc(main-layout, default, pad),
				_misc(main-layout, default, minHeight)
			);

		// XLarge.
			@include breakpoint('<=xlarge') {
				@include thumb(
					_misc(main-layout, xlarge, rows),
					_misc(main-layout, xlarge, columns),
					_misc(main-layout, xlarge, pad),
					_misc(main-layout, xlarge, minHeight)
				);
			}

		// Large.
			@include breakpoint('<=large') {
				@include thumb(
					_misc(main-layout, large, rows),
					_misc(main-layout, large, columns),
					_misc(main-layout, large, pad),
					_misc(main-layout, large, minHeight)
				);
			}

		// Medium.
			@include breakpoint('<=medium') {
				@include thumb(
					_misc(main-layout, medium, rows),
					_misc(main-layout, medium, columns),
					_misc(main-layout, medium, pad),
					_misc(main-layout, medium, minHeight)
				);
			}

		// XSmall.
			@include breakpoint('<=xsmall') {
				@include thumb(
					_misc(main-layout, xsmall, rows),
					_misc(main-layout, xsmall, columns),
					_misc(main-layout, xsmall, pad),
					_misc(main-layout, xsmall, minHeight)
				);
			}

	}