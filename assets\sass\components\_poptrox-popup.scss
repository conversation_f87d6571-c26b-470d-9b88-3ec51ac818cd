///
/// Multiverse by HTML5 UP
/// html5up.net | @ajlkn
/// Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
///

/* Poptrox Popup */

	.poptrox-overlay {
		-webkit-tap-highlight-color: rgba(255,255,255,0);
	}

	.poptrox-popup {
		background: transparentize(_palette(bg-alt), 0.075);
		box-shadow: 0 1em 3em 0.5em rgba(0,0,0,0.25);
		cursor: default;

		&:before {
			@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
			@include vendor('background-image', (
				'linear-gradient(to left, rgba(31,34,36,0.35), rgba(31,34,36,0) 10em, rgba(31,34,36,0))',
				'linear-gradient(to right, rgba(31,34,36,0.35), rgba(31,34,36,0) 10em, rgba(31,34,36,0))'
			));
			content: '';
			display: block;
			height: 100%;
			left: 0;
			position: absolute;
			top: 0;
			width: 100%;
			z-index: 1;
			opacity: 1;
		}

		.closer {
			@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
			background-image: url('images/close.svg');
			background-position: center;
			background-repeat: no-repeat;
			background-size: 3em;
			height: 5em;
			opacity: 0;
			position: absolute;
			right: 0;
			top: 0;
			width: 5em;
			z-index: 2;
		}

		.nav-previous,
		.nav-next {
			@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
			background-image: url('images/arrow.svg');
			background-position: center;
			background-repeat: no-repeat;
			background-size: 5em;
			cursor: pointer;
			height: 8em;
			margin-top: -4em;
			opacity: 0;
			position: absolute;
			top: 50%;
			width: 6em;
			z-index: 2;
		}

		.nav-previous {
			@include vendor('transform', 'scaleX(-1)');
			left: 0;
		}

		.nav-next {
			right: 0;
		}

		.caption {
			@include padding(2em, 2em);
			@include vendor('background-image', 'linear-gradient(to top, rgba(16,16,16,0.45) 25%, rgba(16,16,16,0) 100%)');
			bottom: 0;
			cursor: default;
			left: 0;
			position: absolute;
			text-align: left;
			width: 100%;
			z-index: 2;

			h2, h3, h4, h5, h6 {
				margin: 0 0 (_size(element-margin) * 0.25) 0;
			}

			p {
				color: _palette(fg-bold);
			}
		}

		.loader {
			@include vendor('animation', 'spinner 1s infinite linear !important');
			background-image: url('images/spinner.svg');
			background-position: center;
			background-repeat: no-repeat;
			background-size: contain;
			display: block;
			font-size: 2em;
			height: 2em;
			left: 50%;
			line-height: 2em;
			margin: -1em 0 0 -1em;
			opacity: 0.25;
			position: absolute;
			text-align: center;
			top: 50%;
			width: 2em;
		}

		&:hover {
			.closer,
			.nav-previous,
			.nav-next {
				opacity: 0.5;

				&:hover {
					opacity: 1.0;
				}
			}
		}

		&.loading {
			&:before {
				opacity: 0;
			}
		}

		body.touch & {
			.closer,
			.nav-previous,
			.nav-next {
				opacity: 1.0 !important;
			}
		}

		@include breakpoint('<=medium') {
			.closer {
				background-size: 3em;
			}

			.nav-previous,
			.nav-next {
				background-size: 4em;
			}
		}

		@include breakpoint('<=small') {
			&:before {
				display: none;
			}

			.caption {
				display: none !important;
			}

			.closer,
			.nav-previous,
			.nav-next {
				display: none !important;
			}
		}
	}