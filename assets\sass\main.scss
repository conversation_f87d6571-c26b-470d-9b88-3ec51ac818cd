@import 'libs/vars';
@import 'libs/functions';
@import 'libs/mixins';
@import 'libs/vendor';
@import 'libs/breakpoints';
@import 'fontawesome-all.min.css';
@import url('https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300italic,400,400italic');

/*
	Multiverse by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
*/

// Breakpoints.

	@include breakpoints((
		xlarge:  ( 1281px,  1680px ),
		large:   ( 981px,   1280px ),
		medium:  ( 737px,   980px  ),
		small:   ( 481px,   736px  ),
		xsmall:  ( null,    480px  )
	));

// Spinner.

	@include keyframes(spinner) {
		0% {
			@include vendor('transform', 'rotate(0deg)');
		}

		100% {
			@include vendor('transform', 'rotate(359deg)');
		}
	}

// Base.

	@import 'base/reset';
	@import 'base/page';
	@import 'base/typography';

// Component.

	@import 'components/button';
	@import 'components/form';
	@import 'components/icon';
	@import 'components/list';
	@import 'components/actions';
	@import 'components/icons';
	@import 'components/table';
	@import 'components/panel';
	@import 'components/poptrox-popup';

// Layout.

	@import 'layout/wrapper';
	@import 'layout/header';
	@import 'layout/main';
	@import 'layout/footer';