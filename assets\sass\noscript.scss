@import 'libs/vars';
@import 'libs/functions';
@import 'libs/mixins';
@import 'libs/vendor';
@import 'libs/breakpoints';

/*
	Multiverse by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
*/

/* Wrapper */

	#wrapper {
		body.is-preload & {
			&:before {
				display: none;
			}
		}
	}

/* Main */

	#main {
		body.is-preload & {
			.thumb {
				@include vendor('pointer-events', 'auto');
				opacity: 1;
			}
		}
	}

/* Header */

	#header {
		body.is-preload & {
			@include vendor('transform', 'none');
		}
	}